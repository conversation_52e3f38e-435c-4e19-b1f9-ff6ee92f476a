package main

import (
	"log"
	"net/http"
	"os"
	"wasauch/immer/database"
	"wasauch/immer/handlers"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

func ApiKeyAuthMiddleware() gin.HandlerFunc {
	// get the api key from env
	requiredApiKey := os.Getenv("API_KEY")
	if requiredApiKey == "" {
		log.Fatal("FATAL: API_KEY environment variable not set The application cannot start")
	}

	return func(c *gin.Context) {
		clientApiKey := c.GetHeader("X-API-Key")

		// check api key
		if clientApiKey != requiredApiKey {
			log.Printf("Unauthorized access attempt from %s with key '%s'", c.ClientIP(), clientApiKey)
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			return
		}

		c.Next()
	}
}

func main() {
	err := godotenv.Load()
	if err != nil {
		log.Println("Warning: Could not load env file")
	}

	// connect to sqlite database
	if err := database.InitDB("database.db"); err != nil {
		log.Fatalf("FATAL: Failed to connect to database: %v", err)
	}

	// new gin router
	router := gin.Default()
	router.SetTrustedProxies(nil)

	// set up auth thing
	authMiddleware := ApiKeyAuthMiddleware()

	v1 := router.Group("/v1", authMiddleware)
	{
		// File conversion endpoints
		convertGroup := v1.Group("/convert")
		{
			convertGroup.POST("/raw", handlers.RawConvertHandler)
			convertGroup.POST("/preset", handlers.PresetConvertHandler)
		}

		// Preset Management endpoints
		presetGroup := v1.Group("/presets")
		{
			presetGroup.GET("", handlers.ListPresetsHandler)
			presetGroup.POST("", handlers.CreatePresetHandler)
			presetGroup.GET("/:name", handlers.GetPresetHandler)
			presetGroup.PUT("/:name", handlers.UpdatePresetHandler)
			presetGroup.DELETE("/:name", handlers.DeletePresetHandler)
		}
	}

	log.Println("Server starting on :8080")
	router.Run(":8080")
}