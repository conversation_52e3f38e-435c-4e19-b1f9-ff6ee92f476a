package database

import (
	"database/sql"
	"fmt"
	"log"

	_ "modernc.org/sqlite"
)

// global database variable
var DB *sql.DB

// this holds info for a command parameter
type Parameter struct {
	Name          string
	DefaultValue  sql.NullString
	ParameterType string
}

// this holds info for a conversion preset
type Conversion struct {
	ID                 int
	ToolName           string
	ConversionIDStr    string
	Description        sql.NullString
	InputFormats       string
	OutputFormats      string
	CommandTemplate    string
	IsUserDefined      bool
	Parameters         map[string]Parameter
}

// setting up the database connection
func InitDB(dataSourceName string) error {
	log.Printf("Connecting to database: %s", dataSourceName)
	var err error
	DB, err = sql.Open("sqlite", dataSourceName)
	if err != nil {
		return fmt.Errorf("could not open database: %w", err)
	}

	if err = DB.Ping(); err != nil {
		return fmt.Errorf("could not connect to database: %w", err)
	}

	log.Println("Database connection successful.")
	return nil
}

// gets one preset from the db
func GetConversionDetails(presetName string) (*Conversion, error) {
	// The ORDER BY clause prefers user-defined (1) over default (0) presets.
	query := `
        SELECT
            c.conversion_id,
            t.name,
            c.conversion_id_str,
            c.input_formats,
            c.output_formats,
            c.command_options_template,
            p.name,
            p.default_value,
            p.parameter_type,
            c.is_user_defined,
            c.description
        FROM conversions c
        JOIN tools t ON c.tool_id = t.tool_id
        LEFT JOIN parameters p ON c.conversion_id = p.conversion_id
        WHERE c.conversion_id_str = ?
        ORDER BY c.is_user_defined DESC`

	// run the sql query
	rows, err := DB.Query(query, presetName)
	if err != nil {
		return nil, fmt.Errorf("database query failed: %w", err)
	}
	defer rows.Close()

	var conversion *Conversion
	found := false
	// get only the first row
	if rows.Next() {
		found = true
		var paramName, paramType sql.NullString
		var paramDefaultValue sql.NullString
		var currentConversion Conversion

		err := rows.Scan(
			&currentConversion.ID,
			&currentConversion.ToolName,
			&currentConversion.ConversionIDStr,
			&currentConversion.InputFormats,
			&currentConversion.OutputFormats,
			&currentConversion.CommandTemplate,
			&paramName,
			&paramDefaultValue,
			&paramType,
			&currentConversion.IsUserDefined,
			&currentConversion.Description,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}
		
		conversion = &currentConversion
		conversion.Parameters = make(map[string]Parameter)

		if paramName.Valid {
			conversion.Parameters[paramName.String] = Parameter{
				Name:          paramName.String,
				DefaultValue:  paramDefaultValue,
				ParameterType: paramType.String,
			}
		}

		// get all parameters of a preset
		paramQuery := `SELECT name, default_value, parameter_type FROM parameters WHERE conversion_id = ?`
		paramRows, err := DB.Query(paramQuery, conversion.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to query parameters: %w", err)
		}
		defer paramRows.Close()

		for paramRows.Next() {
			var pName, pType string
			var pDefault sql.NullString
			if err := paramRows.Scan(&pName, &pDefault, &pType); err != nil {
				return nil, fmt.Errorf("failed to scan parameter row: %w", err)
			}
			conversion.Parameters[pName] = Parameter{
				Name: pName,
				DefaultValue: pDefault,
				ParameterType: pType,
			}
		}
	}


	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error during row iteration: %w", err)
	}

	if !found {
		return nil, sql.ErrNoRows
	}

	return conversion, nil
}


// todo: rename function to GetAllConversionPresets 
// prefers user-defined presets
func GetAllConversions() ([]Conversion, error) {
	// sql picks the user preset if it exists, otherwise the default
	query := `
        WITH RankedConversions AS (
            SELECT
                c.conversion_id,
                t.name as tool_name,
                c.conversion_id_str,
                c.input_formats,
                c.output_formats,
                c.command_options_template,
                c.is_user_defined,
                c.description,
                ROW_NUMBER() OVER(PARTITION BY c.conversion_id_str ORDER BY c.is_user_defined DESC) as rn
            FROM conversions c
            JOIN tools t ON c.tool_id = t.tool_id
        )
        SELECT * FROM RankedConversions WHERE rn = 1 ORDER BY conversion_id_str;`

	rows, err := DB.Query(query)
	if err != nil {
		return nil, fmt.Errorf("database query failed for getting all conversions: %w", err)
	}
	defer rows.Close()

	// presets go into this slice
	var conversions []Conversion
	// iterate over query result
	for rows.Next() {
		var conv Conversion
		var rn int
		err := rows.Scan(
			&conv.ID,
			&conv.ToolName,
			&conv.ConversionIDStr,
			&conv.InputFormats,
			&conv.OutputFormats,
			&conv.CommandTemplate,
			&conv.IsUserDefined,
			&conv.Description,
			&rn,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan conversion row: %w", err)
		}
		conversions = append(conversions, conv)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error during conversion rows iteration: %w", err)
	}

	return conversions, nil
}


// create a new preset in the db
func CreateConversion(conv *Conversion) (int64, error) {
	var toolID int
	if conv.ToolName == "ffmpeg" {
		toolID = 1
	} else if conv.ToolName == "imagemagick" {
		toolID = 2
	} else {
		return 0, fmt.Errorf("invalid tool name: %s", conv.ToolName)
	}

	// using a transaction
	tx, err := DB.Begin()
	if err != nil {
		return 0, fmt.Errorf("could not begin transaction: %w", err)
	}

	// put the new conversion in the table
	res, err := tx.Exec(`
        INSERT INTO conversions (tool_id, conversion_id_str, input_formats, output_formats, command_options_template, is_user_defined, description)
        VALUES (?, ?, ?, ?, ?, 1, ?);`,
		toolID, conv.ConversionIDStr, conv.InputFormats, conv.OutputFormats, conv.CommandTemplate, conv.Description)

	if err != nil {
		tx.Rollback()
		return 0, fmt.Errorf("failed to insert new conversion: %w", err)
	}

	newID, err := res.LastInsertId()
	if err != nil {
		tx.Rollback()
		return 0, fmt.Errorf("failed to get last insert ID: %w", err)
	}
	
	// put the parameters in the other table
	for _, param := range conv.Parameters {
		_, err := tx.Exec(`
			INSERT INTO parameters (conversion_id, name, default_value, parameter_type)
			VALUES (?, ?, ?, ?);`, newID, param.Name, param.DefaultValue, param.ParameterType)
		if err != nil {
			tx.Rollback()
			return 0, fmt.Errorf("failed to insert parameter '%s': %w", param.Name, err)
		}
	}
	
	return newID, tx.Commit()
}

// update user preset OR create a user preset
// if it alredy exists by default
func UpdateConversion(presetName string, conv *Conversion) error {
	// search for a user preset with that name

	var existingUserPresetID int
	err := DB.QueryRow("SELECT conversion_id FROM conversions WHERE conversion_id_str = ? AND is_user_defined = 1", presetName).Scan(&existingUserPresetID)

	if err != nil && err != sql.ErrNoRows {
		return fmt.Errorf("error checking for existing user preset: %w", err)
	}
	
	// if no user preset there, create one
	if err == sql.ErrNoRows {
		_, err := CreateConversion(conv)
		return err
	}
	
	// if it is there, update it in a transaction
	tx, err := DB.Begin()
	if err != nil {
		return fmt.Errorf("could not begin transaction for update: %w", err)
	}

	// figure out tool id
	var toolID int
	if conv.ToolName == "ffmpeg" {
		toolID = 1
	} else if conv.ToolName == "imagemagick" {
		toolID = 2
	} else {
		return fmt.Errorf("invalid tool name: %s", conv.ToolName)
	}

	// update main conversion row
	_, err = tx.Exec(`UPDATE conversions SET
		tool_id = ?,
		input_formats = ?,
		output_formats = ?,
		command_options_template = ?,
		description = ?
		WHERE conversion_id = ?`,
		toolID, conv.InputFormats, conv.OutputFormats, conv.CommandTemplate, conv.Description, existingUserPresetID)
	
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update conversion record: %w", err)
	}

	// delete old params
	_, err = tx.Exec("DELETE FROM parameters WHERE conversion_id = ?", existingUserPresetID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete old parameters: %w", err)
	}
	
	// then add new params
	for _, param := range conv.Parameters {
		_, err := tx.Exec(`
			INSERT INTO parameters (conversion_id, name, default_value, parameter_type)
			VALUES (?, ?, ?, ?);`, existingUserPresetID, param.Name, param.DefaultValue, param.ParameterType)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to insert new parameter '%s': %w", param.Name, err)
		}
	}

	return tx.Commit()
}


// delete user preset
func DeleteConversion(presetName string) error {
	// only delete if it is a user preset
	res, err := DB.Exec("DELETE FROM conversions WHERE conversion_id_str = ? AND is_user_defined = 1", presetName)
	if err != nil {
		return fmt.Errorf("database error on delete: %w", err)
	}

	// check if something got deleted
	rowsAffected, err := res.RowsAffected()
	if err != nil {
		return fmt.Errorf("could not get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("preset '%s' not found or it is a default preset that cannot be deleted", presetName)
	}

	return nil
}