# FFmpeg REST API

A REST API service for converting media files using FFmpeg and ImageMagick. The service downloads files from AWS S3, processes them using conversion presets or custom commands, and uploads the results back to S3.

## What This Project Does

This is a Go-based web service that provides a REST API for media file conversion.

## Key Features

- **File Conversion**: Convert media files using FFmpeg or ImageMagick
- **Preset Management**: Create, update, and manage conversion presets for common tasks
- **Raw Commands**: Execute custom FFmpeg/ImageMagick commands
- **S3 Integration**: Automatically downloads source files and uploads converted files to AWS S3
- **SQLite Database**: Stores conversion presets and parameters

## Technologies Used

- **Go 1.24.4**
- **Gin Framework**
- **SQLite**
- **AWS SDK v2**
- **FFmpeg**
- **ImageMagick**

## Project Structure

```
.
├── main.go                 # Main application entry point
├── handlers/               # HTTP request handlers
│   ├── s3_handlers.go     # File conversion endpoints
│   └── preset_handlers.go # Preset management endpoints
├── services/              # Business logic
│   └── s3_processor.go    # S3 download/upload and command execution
├── database/              # Database operations
│   └── db.go             # SQLite database functions
├── database.db           # SQLite database file
├── go.mod                # Go module dependencies
└── README.md             # This file
```

## API Endpoints

All endpoints require an API key in the `X-API-Key` header.

### File Conversion

#### POST /v1/convert/raw

Convert a file using a custom FFmpeg/ImageMagick command.

**Request Body:**

```json
{
  "input_path": "uploads/video.mp4",
  "output_path": "processed/video.webm",
  "command": "ffmpeg -i {input_file} -c:v libvpx-vp9 {output_file}"
}
```

**Example curl request:**

```bash
curl -X POST http://localhost:8080/v1/convert/raw \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-secure-api-key" \
  -d '{
    "input_path": "uploads/video.mp4",
    "output_path": "processed/video.webm",
    "command": "ffmpeg -i {input_file} -vn -b:a 128k {output_file}"
  }'
```

#### POST /v1/convert/preset

Convert a file using a predefined preset.

**Request Body:**

```json
{
  "preset": "mp4_to_webm",
  "input_path": "uploads/video.mp4",
  "output_path": "processed/video.webm",
  "parameters": {
    "video_quality": "23",
    "audio_bitrate": "128"
  }
}
```

**Example curl request:**

```bash
curl -X POST http://localhost:8080/v1/convert/preset \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-secure-api-key" \
  -d '{
    "preset": "mp4_to_webm",
    "input_path": "uploads/video.mp4",
    "output_path": "processed/video.webm",
    "parameters": {
      "video_quality": "23",
      "audio_bitrate": "128"
    }
  }'
```

### Preset Management

#### GET /v1/presets

List all available conversion presets.

**Example curl request:**

```bash
curl -X GET http://localhost:8080/v1/presets \
  -H "X-API-Key: your-secure-api-key"
```

#### POST /v1/presets

Create a new conversion preset.

**Request Body:**

```json
{
  "name": "my_video_preset",
  "tool": "ffmpeg",
  "description": "Convert MP4 video to WebM format with custom quality",
  "input_format": "mp4",
  "output_format": "webm",
  "command_template": "-i {input_file} -c:v libvpx-vp9 -crf {video_quality} -c:a libopus -b:a {audio_bitrate}k {output_file}",
  "parameters": {
    "video_quality": {
      "default_value": "30",
      "type": "number"
    },
    "audio_bitrate": {
      "default_value": "128",
      "type": "number"
    }
  }
}
```

**Example curl request:**

```bash
curl -X POST http://localhost:8080/v1/presets \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-secure-api-key" \
  -d '{
    "name": "my_video_preset",
    "tool": "ffmpeg",
    "description": "Convert MP4 video to WebM format with custom quality",
    "input_format": "mp4",
    "output_format": "webm",
    "command_template": "-i {input_file} -c:v libvpx-vp9 -crf {video_quality} -c:a libopus -b:a {audio_bitrate}k {output_file}",
    "parameters": {
      "video_quality": {
        "default_value": "30",
        "type": "number"
      },
      "audio_bitrate": {
        "default_value": "128",
        "type": "number"
      }
    }
  }'
```

#### GET /v1/presets/:name

Get details of a specific preset.

**Example curl request:**

```bash
curl -X GET http://localhost:8080/v1/presets/mp4_to_webm \
  -H "X-API-Key: your-secure-api-key"
```

#### PUT /v1/presets/:name

Update an existing preset.

**Example curl request:**

```bash
curl -X PUT http://localhost:8080/v1/presets/mp4_to_webm \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-secure-api-key" \
  -d '{
    "tool": "ffmpeg",
    "description": "Updated WebM conversion with better quality",
    "input_format": "mp4",
    "output_format": "webm",
    "command_template": "-i {input_file} -c:v libvpx-vp9 -crf {video_quality} -c:a libopus -b:a {audio_bitrate}k {output_file}",
    "parameters": {
      "video_quality": {
        "default_value": "25",
        "type": "number"
      },
      "audio_bitrate": {
        "default_value": "160",
        "type": "number"
      }
    }
  }'
```

#### DELETE /v1/presets/:name

Delete a preset.

**Example curl request:**

```bash
curl -X DELETE http://localhost:8080/v1/presets/my_video_preset \
  -H "X-API-Key: your-secure-api-key"
```

## Environment Variables

Create a `.env` file in the project root with these variables:

```env
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=your-aws-region
S3_BUCKET_NAME=your-bucket-name
S3_PROCESSED_PREFIX=processed/
API_KEY=your-secure-api-key
```

**Optional S3 Path Configuration:**

- `S3_PROCESSED_PREFIX`: Prefix for processed files (default: "processed/")

## Prerequisites

Before running this application, make sure you have:

1. **Go 1.24.4 or later** installed
2. **FFmpeg** installed and available in your system PATH
3. **ImageMagick** installed and available in your system PATH (if using ImageMagick presets)
4. **AWS S3 bucket** set up with proper permissions
5. **AWS credentials** configured

### Required AWS S3 IAM Permissions

Your AWS credentials must have the following IAM policy attached to access the S3 bucket:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AllowBucketListingForSDK",
      "Effect": "Allow",
      "Action": "s3:ListBucket",
      "Resource": "arn:aws:s3:::your-bucket-name"
    },
    {
      "Sid": "AllowReadFromUploads",
      "Effect": "Allow",
      "Action": ["s3:GetObject"],
      "Resource": ["arn:aws:s3:::your-bucket-name/uploads/*"]
    },
    {
      "Sid": "AllowWriteToProcessed",
      "Effect": "Allow",
      "Action": ["s3:PutObject"],
      "Resource": ["arn:aws:s3:::your-bucket-name/processed/*"]
    }
  ]
}
```

**Note**: Replace `your-bucket-name` with your actual S3 bucket name. This policy ensures the application can:

- List bucket contents (required by AWS SDK)
- Read files from the `uploads/` prefix
- Write converted files to the `processed/` prefix

## Installation and Setup

1. **Clone the repository:**

   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install dependencies:**

   ```bash
   go mod download
   ```

3. **Set up environment variables:**

   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

4. **Run the application:**
   ```bash
   go run main.go
   ```

The server will start on port 8080. You can access it at `http://localhost:8080`.

## How It Works

1. **File Download**: When you make a conversion request, the service downloads the source file from your S3 bucket to a temporary file on the server.

2. **Processing**: The service runs the specified FFmpeg or ImageMagick command on the temporary file, creating a converted output file.

3. **Upload**: The converted file is uploaded back to your S3 bucket at the specified output path.

4. **Cleanup**: All temporary files are automatically deleted after processing.

## Database Schema

The application uses SQLite with these main tables:

- **tools**: Stores information about available tools (FFmpeg, ImageMagick)
- **conversions**: Stores conversion presets with their commands and settings
- **parameters**: Stores parameters for each conversion preset

## Security

**IMPORTANT SECURITY WARNING**: This API is designed for internal use and small automation tasks only. It should NEVER be exposed to the public internet or untrusted networks.

### Why This API Should Not Be Public-Facing:

- **Command Execution**: The API executes arbitrary FFmpeg/ImageMagick commands on the server, which could be exploited if malicious commands are crafted
- **Resource Consumption**: Media processing is CPU and memory intensive - public access could lead to denial of service attacks
- **File System Access**: The service creates temporary files and executes system commands, which poses security risks if exposed
- **Limited Authentication**: Uses simple API key authentication, which is not sufficient for public-facing services
- **No Rate Limiting**: The current implementation lacks rate limiting and request throttling mechanisms

### Recommended Security Practices:

- **Internal Network Only**: Deploy this API behind a firewall, accessible only from your internal network
- **VPN Access**: If remote access is needed, use a VPN to connect to your internal network
- **Reverse Proxy**: If you must expose it, use a reverse proxy with additional security layers (rate limiting, request filtering, etc.)
- **Regular Updates**: Keep FFmpeg, ImageMagick, and all dependencies updated to patch security vulnerabilities
- **Monitor Usage**: Implement logging and monitoring to detect unusual activity
- **Principle of Least Privilege**: Run the service with minimal system permissions

### Current Security Features:

- **API Key Authentication**: All endpoints require a valid API key
- **Input Validation**: Request data is validated before processing
- **Temporary Files**: All temporary files are automatically cleaned up
- **S3 Path Restrictions**: Input files must be in the `uploads/` prefix, output files go to `processed/`

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Missing or invalid API key
- `404 Not Found`: Preset not found
- `409 Conflict`: Preset already exists
- `500 Internal Server Error`: Processing or server errors

## Development

To modify or extend this application:

1. **Add new endpoints**: Create handlers in the `handlers/` directory
2. **Add business logic**: Implement services in the `services/` directory
3. **Database changes**: Modify functions in `database/db.go`
4. **Add middleware**: Implement in `main.go` or separate middleware files

## Known Issues / TODO

- **FFmpeg Command Optimization**: The default FFmpeg commands in the SQLite database need to be optimized and reworked for better performance and quality. This is currently on the TODO list and will be addressed in future updates.

## Common Use Cases

- **Video Conversion**: Convert videos between different formats (MP4, WebM, AVI, etc.)
- **Audio Processing**: Extract audio, change formats, adjust quality
- **Image Processing**: Resize, crop, change formats, apply filters
- **Batch Processing**: Use presets for consistent processing across multiple files
- **Custom Workflows**: Use raw commands for specific processing needs

## Troubleshooting

**Server won't start:**

- Check that all environment variables are set correctly
- Ensure the database file is accessible
- Verify that the specified port (8080) is available

**Conversion fails:**

- Check that FFmpeg/ImageMagick are installed and in PATH
- Verify that the input file exists in S3
- Check AWS credentials and S3 bucket permissions
- Review the command syntax in your preset or raw request

**Database errors:**

- Ensure the SQLite database file has proper read/write permissions

## Contributing

When contributing to this project:

1. Follow Go coding conventions
2. Add appropriate error handling
3. Include logging for debugging
4. Test your changes thoroughly
5. Update documentation as needed
