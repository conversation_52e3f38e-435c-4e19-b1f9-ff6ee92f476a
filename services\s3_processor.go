package services

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

// struct for processed(converted) file location
type ProcessResult struct {
	Bucket string
	Key    string
}

// download a file, run commands, upload the result
func ProcessRawCommand(ctx context.Context, client *s3.Client, bucketName, inputKey, outputKey, commandTemplate string) (*manager.UploadOutput, error) {
	// create a temporary file to stream the S3 download into
	inputTempFile, err := os.CreateTemp("", "s3-raw-input-*.tmp")
	if err != nil {
		return nil, fmt.Errorf("failed to create input temp file: %w", err)
	}
	inputTempFilePath := inputTempFile.Name()
	// ensure the file handle is closed when the function exits
	defer inputTempFile.Close()
	// and ensure the file is deleted
	defer os.Remove(inputTempFilePath) 

	// make another temp file for the conversion output
	outputExt := filepath.Ext(outputKey)
	outputPattern := fmt.Sprintf("s3-raw-output-*%s", outputExt)
	outputTempFile, err := os.CreateTemp("", outputPattern)
	if err != nil {
		return nil, fmt.Errorf("failed to create output temp file with extension %s: %w", outputExt, err)
	}
	outputTempFilePath := outputTempFile.Name()
	outputTempFile.Close()
	defer os.Remove(outputTempFilePath)

	// download the file from s3 to the temp file
	log.Printf("Downloading s3://%s/%s to %s", bucketName, inputKey, inputTempFilePath)
	// make a new s3 downloader
	downloader := manager.NewDownloader(client)

	_, err = downloader.Download(ctx, inputTempFile, &s3.GetObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(inputKey),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to download file from S3: %w", err)
	}
	log.Println("Download complete.")

	// prepare and run the command
	// replace the placeholders with the temp file paths
	replacer := strings.NewReplacer("{input_file}", inputTempFilePath, "{output_file}", outputTempFilePath)
	fullCommand := replacer.Replace(commandTemplate)

	log.Printf("Running command: %s", fullCommand)

	var cmd *exec.Cmd
	// different for windows and linux apparently
	if runtime.GOOS == "windows" {
		cmd = exec.Command("cmd", "/C", fullCommand)
	} else {
		cmd = exec.Command("sh", "-c", fullCommand)
	}

	// run the command, get the output
	cmdOutput, err := cmd.CombinedOutput()
	if err != nil {
		log.Printf("Command failed Output:\n%s", string(cmdOutput))
		return nil, fmt.Errorf("command execution failed: %w Output: %s", err, string(cmdOutput))
	}
	log.Println("ffmpeg processing complete")

	// upload the file to s3
	processedFile, err := os.Open(outputTempFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open processed file for upload: %w", err)
	}
	defer processedFile.Close()

	// make an s3 uploader
	uploader := manager.NewUploader(client)

	log.Printf("Uploading processed file to s3://%s/%s", bucketName, outputKey)
	// upload to s3
	uploadResult, err := uploader.Upload(ctx, &s3.PutObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(outputKey),
		Body:   processedFile,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to upload processed file: %w", err)
	}
	log.Printf("Successfully uploaded to %s", uploadResult.Location)

	return uploadResult, nil
}