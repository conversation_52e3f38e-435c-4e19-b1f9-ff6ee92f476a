package handlers

import (
	"database/sql"
	"fmt"
	"net/http"
	"wasauch/immer/database"

	"github.com/gin-gonic/gin"
)

type PresetAPI struct {
	Name            string                       `json:"name"`
	Tool            string                       `json:"tool"`
	Description     string                       `json:"description,omitempty"`
	InputFormat     string                       `json:"input_format"`
	OutputFormat    string                       `json:"output_format"`
	CommandTemplate string                       `json:"command_template"`
	IsUserDefined   bool                         `json:"is_user_defined"`
	Parameters      map[string]ParameterAPI      `json:"parameters"`
}

type ParameterAPI struct {
	DefaultValue string `json:"default_value"`
	Type         string `json:"type"`
}

// changing database preset to an api preset
func toAPIPreset(dbConv database.Conversion) PresetAPI {
	params := make(map[string]ParameterAPI)
	for name, p := range dbConv.Parameters {
		params[name] = ParameterAPI{
			DefaultValue: p.DefaultValue.String,
			Type:         p.ParameterType,
		}
	}
	return PresetAPI{
		Name:            dbConv.ConversionIDStr,
		Tool:            dbConv.ToolName,
		Description:     dbConv.Description.String,
		InputFormat:     dbConv.InputFormats,
		OutputFormat:    dbConv.OutputFormats,
		CommandTemplate: dbConv.CommandTemplate,
		IsUserDefined:   dbConv.IsUserDefined,
		Parameters:      params,
	}
}


func ListPresetsHandler(c *gin.Context) {
	dbConversions, err := database.GetAllConversions()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve presets", "details": err.Error()})
		return
	}
	
	apiPresets := make([]PresetAPI, 0, len(dbConversions))
	for _, dbConv := range dbConversions {
		// not sending all details for list view
		apiPresets = append(apiPresets, PresetAPI{
			Name: dbConv.ConversionIDStr,
			Tool: dbConv.ToolName,
			Description: dbConv.Description.String,
			InputFormat: dbConv.InputFormats,
			OutputFormat: dbConv.OutputFormats,
			IsUserDefined: dbConv.IsUserDefined,
		})
	}
	
	c.JSON(http.StatusOK, apiPresets)
}

// get a specific preset
func GetPresetHandler(c *gin.Context) {
	presetName := c.Param("name")
	
	dbConv, err := database.GetConversionDetails(presetName)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Preset not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error", "details": err.Error()})
		}
		return
	}
	
	c.JSON(http.StatusOK, toAPIPreset(*dbConv))
}

// create new preset
func CreatePresetHandler(c *gin.Context) {
	var req PresetAPI
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}
	
	// validating user request
	if req.Name == "" || req.Tool == "" || req.InputFormat == "" || req.OutputFormat == "" || req.CommandTemplate == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing required fields: name, tool, input_format, output_format, command_template"})
		return
	}

	// check if a user preset with this name already exists
	existing, err := database.GetConversionDetails(req.Name)
	if err != nil && err != sql.ErrNoRows {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error checking for existing preset"})
		return
	}
	if existing != nil && existing.IsUserDefined {
		c.JSON(http.StatusConflict, gin.H{"error": fmt.Sprintf("A user-defined preset named '%s' already exists.", req.Name)})
		return
	}

	// change api preset into database preset
	dbConv := database.Conversion{
		ConversionIDStr:    req.Name,
		ToolName:           req.Tool,
		Description:        sql.NullString{String: req.Description, Valid: req.Description != ""},
		InputFormats:       req.InputFormat,
		OutputFormats:      req.OutputFormat,
		CommandTemplate:    req.CommandTemplate,
		Parameters:         make(map[string]database.Parameter),
	}
	for name, p := range req.Parameters {
		dbConv.Parameters[name] = database.Parameter{
			Name:          name,
			DefaultValue:  sql.NullString{String: p.DefaultValue, Valid: p.DefaultValue != ""},
			ParameterType: p.Type,
		}
	}
	
	// save to db
	if _, err := database.CreateConversion(&dbConv); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create preset", "details": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{"status": "success", "name": req.Name})
}

// update preset
func UpdatePresetHandler(c *gin.Context) {
	// get preset name from the url
	presetName := c.Param("name")
	var req PresetAPI
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// preset name from url gets prioritized
	req.Name = presetName

	// make a database object from user's json
	dbConv := database.Conversion{
		ConversionIDStr:    req.Name,
		ToolName:           req.Tool,
		Description:        sql.NullString{String: req.Description, Valid: req.Description != ""},
		InputFormats:       req.InputFormat,
		OutputFormats:      req.OutputFormat,
		CommandTemplate:    req.CommandTemplate,
		Parameters:         make(map[string]database.Parameter),
	}
	for name, p := range req.Parameters {
		dbConv.Parameters[name] = database.Parameter{
			Name:          name,
			DefaultValue:  sql.NullString{String: p.DefaultValue, Valid: p.DefaultValue != ""},
			ParameterType: p.Type,
		}
	}

	// update db
	err := database.UpdateConversion(presetName, &dbConv)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update preset", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"status": "success", "name": presetName})
}

// delete a preset
func DeletePresetHandler(c *gin.Context) {
	// get preset name from the url
	presetName := c.Param("name")

	err := database.DeleteConversion(presetName)
	if err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}