package handlers

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
	"wasauch/immer/database"
	"wasauch/immer/services"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/gin-gonic/gin"
)

// getEnvWithDefault returns environment variable value or default if not set
func getEnvWithDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

var (
	// configurable S3 prefix for processed files
	processedPrefix = getEnvWithDefault("S3_PROCESSED_PREFIX", "processed/")
)

type RawConvertRequest struct {
	InputPath  string `json:"input_path" binding:"required"`
	OutputPath string `json:"output_path" binding:"required"`
	Command    string `json:"command" binding:"required"`
}

type PresetConvertRequest struct {
	PresetName string            `json:"preset" binding:"required"`
	InputPath  string            `json:"input_path" binding:"required"`
	OutputPath string            `json:"output_path,omitempty"`
	Parameters map[string]string `json:"parameters,omitempty"`
}


// todo: change name to raw command handler or something like
func RawConvertHandler(c *gin.Context) {
	var req RawConvertRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// only allow ffmpeg command
	if !strings.HasPrefix(req.Command, "ffmpeg") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid command", "details": "Command must start with 'ffmpeg'"})
		return
	}

	// check for bad characters
	if strings.ContainsAny(req.Command, ";&|`$()") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid command", "details": "Command contains forbidden characters"})
		return
	}

	// get bucket name from env
	bucketName := os.Getenv("S3_BUCKET_NAME")
	if bucketName == "" {
		log.Println("Error: S3_BUCKET_NAME environment variable not set")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Server configuration error"})
		return
	}

	// set up aws connection
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		log.Printf("Error loading AWS config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load AWS configuration"})
		return
	}
	client := s3.NewFromConfig(cfg)

	log.Printf("Starting raw conversion for input '%s'", req.InputPath)
	uploadResult, err := services.ProcessRawCommand(
		c.Request.Context(),
		client,
		bucketName,
		req.InputPath,
		req.OutputPath,
		req.Command,
	)
	if err != nil {
		log.Printf("Error processing raw command: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process file", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "File converted and uploaded successfully",
		"result": gin.H{
			"bucket":    bucketName,
			"key":       req.OutputPath,
			"s3_uri":    uploadResult.Location,
			"versionId": uploadResult.VersionID,
		},
	})
}

func PresetConvertHandler(c *gin.Context) {
	var req PresetConvertRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// get the preset from the database
	dbConversion, err := database.GetConversionDetails(req.PresetName)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Preset '%s' not found", req.PresetName)})
		} else {
			log.Printf("Database error looking up preset '%s': %v", req.PresetName, err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		}
		return
	}

	// figure out output file path
	finalOutputPath := req.OutputPath
	if finalOutputPath == "" {
		// if the user did not give a path, make one
		base := strings.TrimSuffix(filepath.Base(req.InputPath), filepath.Ext(req.InputPath))
		finalOutputPath = path.Join(processedPrefix, fmt.Sprintf("%s.%s", base, dbConversion.OutputFormats))
		log.Printf("Output path not provided Generated: %s", finalOutputPath)
	} else {
		// check if the file extension is correct
		expectedExt := "." + dbConversion.OutputFormats
		if !strings.HasSuffix(finalOutputPath, expectedExt) {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Output path extension mismatch",
				"details": fmt.Sprintf("The output_path for preset '%s' must end with '%s'", req.PresetName, expectedExt),
			})
			return
		}
	}

	// build the command
	replacements := make(map[string]string)
	// first use default values
	for name, param := range dbConversion.Parameters {
		if param.DefaultValue.Valid {
			replacements[name] = param.DefaultValue.String
		}
	}
	// then use user's values which can replace default values
	for name, value := range req.Parameters {
		// check if the user sent a parameter that doesn't exist
		if _, ok := dbConversion.Parameters[name]; !ok {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid parameter", "details": fmt.Sprintf("Parameter '%s' is not valid for preset '%s'", name, req.PresetName)})
			return
		}
		replacements[name] = value
	}

	// put values into the command template
	// e.g. replace things like {crf} with 23
	var replacerArgs []string
	for key, val := range replacements {
		replacerArgs = append(replacerArgs, fmt.Sprintf("{%s}", key), val)
	}
	replacer := strings.NewReplacer(replacerArgs...)
	finalCommandSegment := replacer.Replace(dbConversion.CommandTemplate)

	// put the command together (add ffmpeg to the front)
	var finalCommand string
	if strings.HasPrefix(finalCommandSegment, dbConversion.ToolName) {
		finalCommand = finalCommandSegment
	} else {
		finalCommand = dbConversion.ToolName + " " + finalCommandSegment
	}
	// add -y for ffmpeg so it overwrites files
	if dbConversion.ToolName == "ffmpeg" {
		finalCommand = strings.Replace(finalCommand, "ffmpeg ", "ffmpeg -y ", 1)
	}

	// todo: abstract aws-stuff? (dry)
	// set up aws again
	bucketName := os.Getenv("S3_BUCKET_NAME")
	if bucketName == "" {
		log.Println("Error: S3_BUCKET_NAME environment variable not set")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Server configuration error"})
		return
	}

	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		log.Printf("Error loading AWS config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load AWS configuration"})
		return
	}
	client := s3.NewFromConfig(cfg)

	// run the command
	log.Printf("Starting preset conversion '%s' for input '%s'", req.PresetName, req.InputPath)
	uploadResult, err := services.ProcessRawCommand(
		c.Request.Context(),
		client,
		bucketName,
		req.InputPath,
		finalOutputPath,
		finalCommand,
	)
	if err != nil {
		log.Printf("Error processing preset command: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process file", "details": err.Error()})
		return
	}

	// send success message
	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "File converted and uploaded successfully using preset",
		"result": gin.H{
			"preset":    req.PresetName,
			"bucket":    bucketName,
			"key":       finalOutputPath,
			"s3_uri":    uploadResult.Location,
			"versionId": uploadResult.VersionID,
		},
	})
}