module wasauch/immer

go 1.24.4

require (
	github.com/aws/aws-sdk-go-v2 v1.36.5
	github.com/aws/aws-sdk-go-v2/config v1.29.17
	github.com/aws/aws-sdk-go-v2/feature/s3/manager v1.17.81
	github.com/aws/aws-sdk-go-v2/service/s3 v1.81.0
	github.com/gin-contrib/cors v1.7.6
	github.com/gin-gonic/gin v1.10.1
	github.com/joho/godotenv v1.5.1
	github.com/mattn/go-sqlite3 v1.14.28
	modernc.org/sqlite v1.38.0
)

require (
	github.com/aws/aws-sdk-go-v2/aws/protocol/eventstream v1.6.11
	github.com/aws/aws-sdk-go-v2/credentials v1.17.70
	github.com/aws/aws-sdk-go-v2/feature/ec2/imds v1.16.32
	github.com/aws/aws-sdk-go-v2/internal/configsources v1.3.36
	github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.6.36
	github.com/aws/aws-sdk-go-v2/internal/ini v1.8.3
	github.com/aws/aws-sdk-go-v2/internal/v4a v1.3.36
	github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.12.4
	github.com/aws/aws-sdk-go-v2/service/internal/checksum v1.7.4
	github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.12.17
	github.com/aws/aws-sdk-go-v2/service/internal/s3shared v1.18.17
	github.com/aws/aws-sdk-go-v2/service/sso v1.25.5
	github.com/aws/aws-sdk-go-v2/service/ssooidc v1.30.3
	github.com/aws/aws-sdk-go-v2/service/sts v1.34.0
	github.com/aws/smithy-go v1.22.4
	github.com/bytedance/sonic v1.13.3
	github.com/bytedance/sonic/loader v0.2.4
	github.com/cloudwego/base64x v0.1.5
	github.com/cloudwego/iasm v0.2.0
	github.com/dustin/go-humanize v1.0.1
	github.com/gabriel-vasile/mimetype v1.4.9
	github.com/gin-contrib/sse v1.1.0
	github.com/go-playground/locales v0.14.1
	github.com/go-playground/universal-translator v0.18.1
	github.com/go-playground/validator/v10 v10.26.0
	github.com/goccy/go-json v0.10.5
	github.com/google/uuid v1.6.0
	github.com/json-iterator/go v1.1.12
	github.com/klauspost/cpuid/v2 v2.2.10
	github.com/leodido/go-urn v1.4.0
	github.com/mattn/go-isatty v0.0.20
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
	github.com/modern-go/reflect2 v1.0.2
	github.com/ncruces/go-strftime v0.1.9
	github.com/pelletier/go-toml/v2 v2.2.4
	github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec
	github.com/twitchyliquid64/golang-asm v0.15.1
	github.com/ugorji/go/codec v1.3.0
	golang.org/x/arch v0.18.0
	golang.org/x/crypto v0.39.0
	golang.org/x/exp v0.0.0-20250408133849-7e4ce0ab07d0
	golang.org/x/net v0.41.0
	golang.org/x/sys v0.33.0
	golang.org/x/text v0.26.0
	google.golang.org/protobuf v1.36.6
	gopkg.in/yaml.v3 v3.0.1
	modernc.org/libc v1.65.10
	modernc.org/mathutil v1.7.1
	modernc.org/memory v1.11.0
)
